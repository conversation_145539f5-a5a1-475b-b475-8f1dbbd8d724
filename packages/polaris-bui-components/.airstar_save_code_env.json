{"appId": "6847d44bc63a45e78048bb9e", "appName": "polaris-bui-components", "components": [{"appId": "6847d44bc63a45e78048bb9e", "assetId": "easy-form-filter-v1", "code": "https://cdnfile.corp.kuaishou.com/kc/files/a/airstar/polaris-bui-components-easy-form-filter-v1-as1nkjv9/easy-form-filter-v1.cjs.js", "syncCode": "new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('[lux-info]easy-form-filter-v1 in iframe'); topWindow = topWindow.parent; } const lockName = '_easy-form-filter-v1_Lock'; const eventName = '_easy-form-filter-v1_LockReleased'; const finalStatusFlag = '_easy-form-filter-v1_FinalStatus'; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === 'success') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('script'); script.src = 'https://cdnfile.corp.kuaishou.com/kc/files/a/airstar/polaris-bui-components-easy-form-filter-v1-as1nkjv9/easy-form-filter-v1.cjs.js'; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('[lux-info]easy-form-filter-v1 consumed', perfLoss, 'ms to finish load, timeout:', timeout); resolved = true; if (timeout) { releaseLock('failed'); reject('[lux-info]easy-form-filter-v1 Load timeout'); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: 'airstar-load-custom-components-per-loss', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: 'easy-form-filter-v1', perfLoss, }, }); releaseLock('success'); delete topWindow[lockName]; resolve(); } }; window['_easy-form-filter-v1-v1.cjs.js_resolver'] = resolveWrapper; topWindow['_easy-form-filter-v1-v1.cjs.js_resolver'] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('[lux-info]easy-form-filter-v1 load failed', e); clearTimeout(timeoutId); releaseLock('failed'); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('[lux-info]easy-form-filter-v1 Another instance is already running, waiting for lock release.'); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === 'success') { console.log('[lux-info]easy-form-filter-v1 resolve directly'); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('[lux-info]easy-form-filter-v1 acquire lock again'); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('[lux-info]easy-form-filter-v1 执行垫片逻辑时报错', error); reject(error); } });", "currentVersionNo": 59, "componentName": "easy-form-filter-v1", "buildHash": "ugz3gcxo", "deployTime": "2025-07-29T03:51:55.227Z"}], "deployTime": "2025-07-29T03:51:55.227Z", "buildHash": "ugz3gcxo", "totalComponents": 1, "branch": "unknown", "commitId": "unknown", "buildType": "vite-umd", "pipelineVersion": "2.0"}