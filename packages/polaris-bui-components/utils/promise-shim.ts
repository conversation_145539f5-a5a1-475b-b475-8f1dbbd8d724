/**
 * Promise 包装器（Shim 逻辑）- 基于 material 文件夹的实现
 * 
 * 将 CDN URL 包装成 Promise 代码，支持超时、重试、锁机制等功能
 */

interface ShimOptions {
  timeout?: number;
  lockPrefix?: string;
  cdnHosts?: string[];
  debug?: boolean;
}

interface ComponentPromiseOptions {
  componentName: string;
  cdnUrl: string;
  cssUrl?: string;
  timeout?: number;
  lockPrefix?: string;
  debug?: boolean;
}

/**
 * 为组件生成 Promise 包装代码
 */
export function generateComponentPromise(options: ComponentPromiseOptions): string {
  const {
    componentName,
    cdnUrl,
    cssUrl,
    timeout = 3000,
    lockPrefix = '_component_Lock',
    debug = false
  } = options;

  return `
// Promise 包装代码 for ${componentName}
(function() {
  const componentName = '${componentName}';
  const cdnUrl = '${cdnUrl}';
  const cssUrl = '${cssUrl || ''}';
  const timeout = ${timeout};
  const lockPrefix = '${lockPrefix}';
  const debug = ${debug};
  
  // 锁机制，防止重复加载
  const lockKey = lockPrefix + '_' + componentName;
  if (window[lockKey]) {
    if (debug) {
      console.log('[Promise Shim] 组件已在加载中:', componentName);
    }
    return window[lockKey];
  }
  
  let resolveWrapper;
  let rejectWrapper;
  let timeoutId;
  
  // 创建Promise
  const promise = new Promise((resolve, reject) => {
    resolveWrapper = (success) => {
      clearTimeout(timeoutId);
      delete window[lockKey];
      
      if (success) {
        if (debug) {
          console.log('[Promise Shim] 组件加载成功:', componentName);
        }
        resolve(true);
      } else {
        if (debug) {
          console.log('[Promise Shim] 组件加载失败:', componentName);
        }
        reject(new Error('组件加载超时或失败: ' + componentName));
      }
    };
    
    rejectWrapper = (error) => {
      clearTimeout(timeoutId);
      delete window[lockKey];
      
      if (debug) {
        console.log('[Promise Shim] 组件加载错误:', componentName, error);
      }
      reject(error);
    };
  });
  
  // 设置锁
  window[lockKey] = promise;
  
  // 检测正确的 window 环境（支持代理 window）
  const targetWindow = window.airstarEntry?.proxyWindow || window;
  
  // 设置 resolver 供组件调用
  targetWindow['_' + componentName + '_resolver'] = resolveWrapper;
  
  // 输出调试信息
  if (debug) {
    console.log('[Promise Shim] 设置 resolver 到 window:', targetWindow === window ? 'global' : 'proxied', 'for', componentName);
  }
  
  // 设置超时
  timeoutId = setTimeout(() => {
    if (debug) {
      console.log('[Promise Shim] 组件加载超时:', componentName);
    }
    resolveWrapper(false);
  }, timeout);
  
  // 加载CSS（如果有）
  if (cssUrl) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = cssUrl;
    link.onerror = () => {
      if (debug) {
        console.warn('[Promise Shim] CSS加载失败:', cssUrl);
      }
      // CSS加载失败不影响组件加载
    };
    document.head.appendChild(link);
  }
  
  // 加载JS
  const script = document.createElement('script');
  script.src = cdnUrl;
  script.async = true;
  
  script.onload = () => {
    if (debug) {
      console.log('[Promise Shim] 脚本加载完成:', cdnUrl);
    }
    // 脚本加载完成，等待组件调用resolver
  };
  
  script.onerror = () => {
    if (debug) {
      console.error('[Promise Shim] 脚本加载失败:', cdnUrl);
    }
    rejectWrapper(new Error('脚本加载失败: ' + cdnUrl));
  };
  
  document.head.appendChild(script);
  
  return promise;
})()`;
}

/**
 * 为组件生成带CDN回退的 Promise 包装代码
 */
export function generateComponentPromiseWithCDNFallback(options: ComponentPromiseOptions & { cdnHosts?: string[] }): string {
  const {
    componentName,
    cdnUrl,
    cssUrl,
    timeout = 3000,
    lockPrefix = '_component_Lock',
    debug = false,
    cdnHosts = []
  } = options;

  // 生成CDN回退URL列表
  const fallbackUrls = cdnHosts.map(host => {
    const url = new URL(cdnUrl);
    return `https://${host}${url.pathname}`;
  });

  return `
// Promise 包装代码 with CDN fallback for ${componentName}
(function() {
  const componentName = '${componentName}';
  const primaryUrl = '${cdnUrl}';
  const fallbackUrls = ${JSON.stringify(fallbackUrls)};
  const cssUrl = '${cssUrl || ''}';
  const timeout = ${timeout};
  const lockPrefix = '${lockPrefix}';
  const debug = ${debug};
  
  // 锁机制，防止重复加载
  const lockKey = lockPrefix + '_' + componentName;
  if (window[lockKey]) {
    if (debug) {
      console.log('[Promise Shim] 组件已在加载中:', componentName);
    }
    return window[lockKey];
  }
  
  let resolveWrapper;
  let rejectWrapper;
  let timeoutId;
  let currentUrlIndex = -1;
  
  // 创建Promise
  const promise = new Promise((resolve, reject) => {
    resolveWrapper = (success) => {
      clearTimeout(timeoutId);
      delete window[lockKey];
      
      if (success) {
        if (debug) {
          console.log('[Promise Shim] 组件加载成功:', componentName);
        }
        resolve(true);
      } else {
        if (debug) {
          console.log('[Promise Shim] 组件加载失败:', componentName);
        }
        reject(new Error('组件加载超时或失败: ' + componentName));
      }
    };
    
    rejectWrapper = (error) => {
      clearTimeout(timeoutId);
      delete window[lockKey];
      
      if (debug) {
        console.log('[Promise Shim] 组件加载错误:', componentName, error);
      }
      reject(error);
    };
  });
  
  // 设置锁
  window[lockKey] = promise;
  
  // 检测正确的 window 环境（支持代理 window）
  const targetWindow = window.airstarEntry?.proxyWindow || window;
  
  // 设置 resolver 供组件调用
  targetWindow['_' + componentName + '_resolver'] = resolveWrapper;
  
  // 输出调试信息
  if (debug) {
    console.log('[Promise Shim] 设置 resolver 到 window:', targetWindow === window ? 'global' : 'proxied', 'for', componentName);
  }
  
  // 设置超时
  timeoutId = setTimeout(() => {
    if (debug) {
      console.log('[Promise Shim] 组件加载超时:', componentName);
    }
    resolveWrapper(false);
  }, timeout);
  
  // 尝试加载脚本的函数
  function tryLoadScript() {
    currentUrlIndex++;
    const urls = [primaryUrl, ...fallbackUrls];
    
    if (currentUrlIndex >= urls.length) {
      rejectWrapper(new Error('所有CDN都加载失败: ' + componentName));
      return;
    }
    
    const currentUrl = urls[currentUrlIndex];
    
    if (debug) {
      console.log('[Promise Shim] 尝试加载:', currentUrl, '(尝试', currentUrlIndex + 1, '/', urls.length, ')');
    }
    
    const script = document.createElement('script');
    script.src = currentUrl;
    script.async = true;
    
    script.onload = () => {
      if (debug) {
        console.log('[Promise Shim] 脚本加载完成:', currentUrl);
      }
      // 脚本加载完成，等待组件调用resolver
    };
    
    script.onerror = () => {
      if (debug) {
        console.warn('[Promise Shim] 脚本加载失败，尝试下一个CDN:', currentUrl);
      }
      // 移除失败的script标签
      document.head.removeChild(script);
      // 尝试下一个URL
      tryLoadScript();
    };
    
    document.head.appendChild(script);
  }
  
  // 加载CSS（如果有）
  if (cssUrl) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = cssUrl;
    link.onerror = () => {
      if (debug) {
        console.warn('[Promise Shim] CSS加载失败:', cssUrl);
      }
      // CSS加载失败不影响组件加载
    };
    document.head.appendChild(link);
  }
  
  // 开始尝试加载脚本
  tryLoadScript();
  
  return promise;
})()`;
}

/**
 * 生成 Shim 代码的工具类
 */
export class ShimGenerator {
  private options: ShimOptions;
  
  constructor(options: ShimOptions = {}) {
    this.options = {
      timeout: 3000,
      lockPrefix: '_component_Lock',
      cdnHosts: [],
      debug: false,
      ...options
    };
  }
  
  /**
   * 为组件生成 Promise 代码
   */
  generateForComponent(componentName: string, cdnUrl: string, cssUrl?: string): string {
    return generateComponentPromise({
      componentName,
      cdnUrl,
      cssUrl,
      ...this.options
    });
  }
  
  /**
   * 为组件生成带CDN回退的 Promise 代码
   */
  generateForComponentWithCDNFallback(componentName: string, cdnUrl: string, cssUrl?: string): string {
    return generateComponentPromiseWithCDNFallback({
      componentName,
      cdnUrl,
      cssUrl,
      ...this.options
    });
  }
}

export default ShimGenerator;
