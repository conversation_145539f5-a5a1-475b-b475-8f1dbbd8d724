# 分层构建系统 - polaris-bui-components

基于 airstar-material 的实现，提供分层的构建命令，支持分步验证和测试。

## 🚀 快速开始

### 基础构建命令

```bash
# 生成Bootstrap入口文件
pnpm run generate:bootstrap

# 纯UMD构建（不包含Pipeline流程）
pnpm run build:umd
pnpm run build:umd:debug
```

### Pipeline流程命令

```bash
# 完整流程（构建 + Promise生成 + API调用）
pnpm run pipeline

# 仅构建模式（只构建，不上传不调用API）
pnpm run pipeline:build-only

# 干跑模式（测试所有步骤，不实际执行）
pnpm run pipeline:dry-run
```

### 分步控制命令

```bash
# 跳过构建，使用现有产物
pnpm run pipeline:skip-build

# 跳过上传，仅构建
pnpm run pipeline:skip-upload

# 跳过API调用
pnpm run pipeline:skip-api

# 调试模式
pnpm run pipeline:debug
```

### 分步验证命令

```bash
# 测试构建流程
pnpm run test:build

# 测试上传流程
pnpm run test:upload

# 测试API流程
pnpm run test:api
```

## 📋 命令详解

### 1. 基础构建命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `generate:bootstrap` | 生成Bootstrap入口文件 | 为所有组件生成包含registerRenderer的入口文件 |
| `build:umd` | UMD构建 | 纯构建，不包含Pipeline流程 |
| `build:umd:debug` | UMD调试构建 | 开启调试模式的UMD构建 |

### 2. Pipeline流程命令

| 命令 | 功能 | 包含步骤 |
|------|------|----------|
| `pipeline` | 完整流程 | 构建 + Promise生成 + API调用 |
| `pipeline:build-only` | 仅构建 | 构建 |
| `pipeline:dry-run` | 干跑测试 | 模拟所有步骤 |
| `pipeline:debug` | 调试模式 | 完整流程 + 调试信息 |

### 3. 分步控制命令

| 命令 | 跳过步骤 | 执行步骤 |
|------|----------|----------|
| `pipeline:skip-build` | 构建 | Promise生成 + API调用 |
| `pipeline:skip-upload` | Promise生成 | 构建 |
| `pipeline:skip-api` | API调用 | 构建 + Promise生成 |

### 4. 分步验证命令

| 命令 | 功能 | 等价于 |
|------|------|--------|
| `test:build` | 测试构建 | `pipeline:dry-run --debug` |
| `test:upload` | 测试上传 | `pipeline:skip-api --debug` |
| `test:api` | 测试API | `pipeline:skip-build --skip-upload --debug` |

## 🔧 高级用法

### 指定组件构建

```bash
# 构建指定组件
pnpm run pipeline -- --components=base-middleware,polaris-co-table

# 测试指定组件
pnpm run test:build -- --components=base-middleware
```

### 组合参数

```bash
# 调试模式 + 干跑
pnpm run pipeline -- --dry-run --debug

# 跳过构建 + 调试模式
pnpm run pipeline -- --skip-build --debug

# 仅构建 + 指定组件
pnpm run pipeline -- --build-only --components=base-middleware
```

### 查看帮助

```bash
pnpm run pipeline -- --help
```

## 📁 构建产物

### 构建成功后的文件结构

```
dist/
├── base-middleware.umd.js          # UMD组件文件
├── base-middleware.css             # CSS样式文件（如果有）
├── base-middleware.promise.js      # Promise包装代码（带CDN回退）
├── base-middleware.sync.js         # 同步Promise代码
├── polaris-co-table.umd.js
├── polaris-co-table.css
├── polaris-co-table.promise.js
└── polaris-co-table.sync.js
```

### Bootstrap文件结构

```
components/
├── base/middleware/bootstrap/index.ts    # 自动生成的Bootstrap入口
├── polaris-co-table/bootstrap/index.ts
└── ...
```

## 🧪 测试流程

### 1. 测试构建是否成功

```bash
# 快速测试构建
pnpm run test:build

# 详细测试构建
pnpm run pipeline:dry-run --debug
```

### 2. 验证Promise代码生成

```bash
# 测试Promise生成
pnpm run test:upload

# 检查生成的Promise文件
ls dist/*.promise.js
```

### 3. 验证API调用

```bash
# 测试API流程
pnpm run test:api

# 使用现有产物测试API
pnpm run pipeline:skip-build --skip-upload --debug
```

## 🔍 故障排除

### 构建失败

1. **检查Bootstrap文件**
   ```bash
   pnpm run generate:bootstrap
   ```

2. **检查组件入口文件**
   - 确保组件有 `bootstrap/index.ts` 或 `src/App.tsx` 或 `src/index.tsx`

3. **调试构建过程**
   ```bash
   pnpm run test:build
   ```

### Promise代码生成失败

1. **检查构建产物**
   ```bash
   ls dist/*.umd.js
   ```

2. **测试Promise生成**
   ```bash
   pnpm run test:upload
   ```

### API调用失败

1. **测试API流程**
   ```bash
   pnpm run test:api
   ```

2. **检查API配置**
   - 确保有正确的API配置文件

## 📊 性能监控

### 构建时间监控

```bash
# 查看详细构建时间
pnpm run pipeline:debug
```

### 文件大小监控

构建完成后会显示每个组件的文件大小：

```
✅ 成功构建: 2 个组件

成功组件:
  - base-middleware: 109.59KB (804ms)
  - polaris-co-table: 83.25KB + CSS(4.00KB) (650ms)
```

## 🔗 相关文件

- `scripts/vite-pipeline-new.ts` - 主Pipeline脚本
- `scripts/core/build-umd.ts` - UMD构建器
- `scripts/core/generate-bootstrap.ts` - Bootstrap生成器
- `utils/promise-shim.ts` - Promise包装器
- `vite.umd.config.ts` - UMD构建配置

## 💡 最佳实践

1. **开发时**：使用 `test:build` 快速验证构建
2. **测试时**：使用 `pipeline:dry-run` 测试完整流程
3. **部署前**：使用 `pipeline:build-only` 生成产物
4. **调试时**：添加 `--debug` 参数查看详细信息
5. **CI/CD**：使用 `pipeline` 执行完整流程
