{"name": "@polaris/bui-components", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "generate:bootstrap": "tsx scripts/core/generate-bootstrap.ts", "build:umd": "tsx scripts/core/build-umd.ts", "build:umd:debug": "tsx scripts/core/build-umd.ts --debug", "pipeline": "tsx scripts/vite-pipeline-new.ts --build-mode=umd", "pipeline:dry-run": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --dry-run", "pipeline:debug": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --debug", "pipeline:skip-build": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-build", "pipeline:skip-upload": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-upload", "pipeline:skip-api": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-api", "pipeline:build-only": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-upload --skip-api", "test:build": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --dry-run --debug", "test:upload": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-api --debug", "test:api": "tsx scripts/vite-pipeline-new.ts --build-mode=umd --skip-build --skip-upload --debug", "upload": "echo '⚠️  请使用分层命令: pnpm run pipeline (完整流程) | pnpm run pipeline:build-only (仅构建) | pnpm run pipeline:dry-run (测试)'", "upload:help": "echo '💡 可用命令: pipeline, pipeline:dry-run, pipeline:build-only, test:build, test:upload, test:api'"}, "dependencies": {"@kuaishou/kop-ui": "^1.45.11", "@types/lodash": "^4.14.182", "amis": "6.5.0", "amis-core": "6.5.0", "axios": "^1.6.8", "lodash": "^4.17.21", "moment": "^2.29.4", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@types/node": "^18.15.13", "@vitejs/plugin-vue": "^5.0.4", "tsx": "^4.7.1", "typescript": "^5.0.4", "vite": "^5.2.0"}}