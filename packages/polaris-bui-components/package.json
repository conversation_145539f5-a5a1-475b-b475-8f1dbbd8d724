{"name": "@polaris/bui-components", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:umd": "tsx scripts/core/build-umd.ts", "build:umd:debug": "tsx scripts/core/build-umd.ts --debug", "generate:bootstrap": "tsx scripts/core/generate-bootstrap.ts", "pipeline": "tsx scripts/vite-pipeline-new.ts", "pipeline:debug": "tsx scripts/vite-pipeline-new.ts --debug", "pipeline:skip-build": "tsx scripts/vite-pipeline-new.ts --skip-build", "pipeline:umd": "tsx scripts/vite-pipeline.ts --build-mode=umd", "pipeline:esm": "tsx scripts/vite-pipeline.ts --build-mode=esm", "upload": "echo '⚠️  upload命令已删除，请使用 pnpm run pipeline'", "upload:help": "echo '💡 使用 pnpm run pipeline -- --help 查看帮助'"}, "dependencies": {"@kuaishou/kop-ui": "^1.45.11", "@types/lodash": "^4.14.182", "amis": "6.5.0", "amis-core": "6.5.0", "axios": "^1.6.8", "lodash": "^4.17.21", "moment": "^2.29.4", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@types/node": "^18.15.13", "@vitejs/plugin-vue": "^5.0.4", "tsx": "^4.7.1", "typescript": "^5.0.4", "vite": "^5.2.0"}}