#!/usr/bin/env node

/**
 * Bootstrap 入口文件生成器 - 基于 material 文件夹的实现
 *
 * 为每个组件自动生成包含 registerRenderer 调用的 bootstrap 入口文件
 * 使用通用正则识别入口文件，middleware单独处理
 */

import { existsSync, readdirSync, statSync, mkdirSync, writeFileSync, readFileSync } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// ES模块兼容性：获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface ComponentConfig {
  componentInfo?: {
    name?: string;
    type?: string;
  };
  assetId?: string;
  appId?: string;
  appName?: string;
}

interface ComponentInfo {
  name: string;
  path: string;
  config?: ComponentConfig;
  hasAppTsx: boolean;
  hasIndexTsx: boolean;
  hasEntryTsx: boolean;
  type: 'base' | 'service' | 'table' | 'filter' | 'complex';
}

class BootstrapGenerator {
  private componentsDir: string;

  constructor(componentsDir: string = path.join(__dirname, '../../components')) {
    this.componentsDir = componentsDir;
  }

  /**
   * 扫描所有组件 - 使用通用正则识别入口文件
   */
  private scanComponents(): ComponentInfo[] {
    console.log(`[Bootstrap Generator] 扫描组件目录: ${this.componentsDir}`);

    if (!existsSync(this.componentsDir)) {
      throw new Error(`组件目录不存在: ${this.componentsDir}`);
    }

    const components: ComponentInfo[] = [];
    const items = readdirSync(this.componentsDir);

    for (const item of items) {
      const itemPath = path.join(this.componentsDir, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory()) {
        // 特殊处理base目录 - 只处理middleware组件
        if (item === 'base') {
          console.log(`📁 扫描base目录: ${itemPath} (仅处理middleware)`);
          const middlewarePath = path.join(itemPath, 'middleware');
          if (existsSync(middlewarePath) && statSync(middlewarePath).isDirectory()) {
            const componentInfo = this.analyzeComponent('base-middleware', middlewarePath);
            if (componentInfo) {
              console.log(`📦 添加base组件: base-middleware`);
              components.push(componentInfo);
            }
          } else {
            console.warn(`⚠️  base/middleware 目录不存在，跳过base组件处理`);
          }
          continue;
        }

        // 处理其他组件目录
        const componentInfo = this.analyzeComponent(item, itemPath);
        if (componentInfo) {
          components.push(componentInfo);
        }
      }
    }

    console.log(`[Bootstrap Generator] 发现 ${components.length} 个组件:`, components.map(c => c.name));
    return components;
  }

  /**
   * 分析组件信息 - 使用通用正则识别入口文件
   */
  private analyzeComponent(name: string, componentPath: string): ComponentInfo | null {
    try {
      // 读取组件配置（如果存在）
      let config: ComponentConfig | undefined;
      const configPath = path.join(componentPath, 'const.json');
      if (existsSync(configPath)) {
        try {
          const configContent = readFileSync(configPath, 'utf-8');
          config = JSON.parse(configContent);
        } catch (error) {
          console.warn(`[Bootstrap Generator] 组件 ${name} 配置文件解析失败:`, error);
        }
      }

      // 检查入口文件 - 使用通用正则识别
      const possibleEntries = [
        'src/App.tsx',
        'src/App.ts',
        'src/index.tsx',
        'src/index.ts',
        'entry.tsx',
        'entry.ts',
        'react_app/index.tsx',
        'react_app/index.ts'
      ];

      const hasAppTsx = existsSync(path.join(componentPath, 'src/App.tsx'));
      const hasIndexTsx = existsSync(path.join(componentPath, 'src/index.tsx'));
      const hasEntryTsx = existsSync(path.join(componentPath, 'entry.tsx'));

      // 至少需要有一个入口文件
      const hasAnyEntry = possibleEntries.some(entry =>
        existsSync(path.join(componentPath, entry))
      );

      if (!hasAnyEntry) {
        console.warn(`[Bootstrap Generator] 组件 ${name} 缺少入口文件，跳过`);
        return null;
      }

      // 确定组件类型
      const type = this.determineComponentType(name);

      return {
        name,
        path: componentPath,
        config,
        hasAppTsx,
        hasIndexTsx,
        hasEntryTsx,
        type
      };
    } catch (error) {
      console.error(`[Bootstrap Generator] 分析组件 ${name} 失败:`, error);
      return null;
    }
  }

  /**
   * 确定组件类型
   */
  private determineComponentType(name: string): 'base' | 'service' | 'table' | 'filter' | 'complex' {
    if (name.includes('base')) return 'base';
    if (name.includes('service')) return 'service';
    if (name.includes('table')) return 'table';
    if (name.includes('filter')) return 'filter';
    return 'complex';
  }

  /**
   * 生成 bootstrap 入口文件 - 参考 material 实现
   */
  private generateBootstrapFile(component: ComponentInfo): void {
    const bootstrapDir = path.join(component.path, 'bootstrap');

    // 确保 bootstrap 目录存在
    if (!existsSync(bootstrapDir)) {
      mkdirSync(bootstrapDir, { recursive: true });
    }

    // 确定入口文件路径 - 优先级：App.tsx > index.tsx > entry.tsx
    let entryFile = '../src/index';
    if (component.hasAppTsx) {
      entryFile = '../src/App';
    } else if (component.hasIndexTsx) {
      entryFile = '../src/index';
    } else if (component.hasEntryTsx) {
      entryFile = '../entry';
    } else {
      // 查找react_app入口
      if (existsSync(path.join(component.path, 'react_app/index.tsx'))) {
        entryFile = '../react_app/index';
      }
    }

    // 确定组件类型
    const componentType = component.config?.componentInfo?.name || component.name;

    // 生成 bootstrap 内容 - 参考 material 的实现
    const bootstrapContent = `/**
 * Bootstrap 入口文件 - ${component.name}
 *
 * 基于 material 文件夹的正确实现
 * 包含 Amis 注册逻辑，确保组件能够正确注册到 Amis 系统中
 */

import { registerRenderer } from 'amis';
import Component from '${entryFile}';

// 组件配置信息
const componentInfo = {
  name: '${component.name}',
  type: '${componentType}',
  description: '${component.name} 组件',
  assetId: '${component.config?.assetId || ''}',
  appId: '${component.config?.appId || ''}',
};

// 注册组件到 Amis 系统
registerRenderer({
  type: componentInfo.type,
  autoVar: true,
  component: Component,
});

// 导出组件供 UMD 使用
export default Component;

// 导出组件信息
export { componentInfo };

// 确保在 UMD 环境中全局可用
if (typeof window !== 'undefined') {
  // 在 window 上设置组件引用，防止被垃圾回收
  (window as any).__BUI_COMPONENT_${component.name.replace(/[-]/g, '_')}__ = Component;

  // 输出调试信息
  console.log('[Bootstrap] ${component.name} 组件已注册');

  // 检测正确的 window 环境（支持代理 window）
  const targetWindow = window.airstarEntry?.proxyWindow || window;

  // 输出调试信息
  console.log('[Bootstrap] ${component.name} 使用 window:', targetWindow === window ? 'global' : 'proxied');

  // 调用 resolver 解锁 Promise（按照原始框架模式）
  if (typeof targetWindow['_${component.name}_resolver'] === 'function') {
    targetWindow['_${component.name}_resolver'](true);
    console.log('[Bootstrap] ${component.name} resolver 调用成功');
  } else {
    console.log('[Bootstrap] ${component.name} resolver 未找到');
  }
}`;

    // 写入文件
    const bootstrapFilePath = path.join(bootstrapDir, 'index.ts');
    writeFileSync(bootstrapFilePath, bootstrapContent);

    console.log(`[Bootstrap Generator] 生成 ${component.name} 的 bootstrap 文件: ${bootstrapFilePath}`);
  }

  /**
   * 生成所有组件的 bootstrap 文件
   */
  public generateAll(): void {
    console.log(`[Bootstrap Generator] 开始生成 Bootstrap 文件...`);

    const components = this.scanComponents();

    if (components.length === 0) {
      console.log('[Bootstrap Generator] 没有发现任何组件');
      return;
    }

    for (const component of components) {
      this.generateBootstrapFile(component);
    }

    console.log(`[Bootstrap Generator] 生成完成！共处理 ${components.length} 个组件`);
  }

  /**
   * 为指定组件生成 bootstrap 文件
   */
  public generateForComponent(componentName: string): void {
    console.log(`[Bootstrap Generator] 为组件 ${componentName} 生成 Bootstrap 文件...`);

    const components = this.scanComponents();
    const component = components.find(c => c.name === componentName);

    if (!component) {
      throw new Error(`未找到组件: ${componentName}`);
    }

    this.generateBootstrapFile(component);
    console.log(`[Bootstrap Generator] 组件 ${componentName} 的 Bootstrap 文件生成完成`);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const args = process.argv.slice(2);
    const componentName = args.find(arg => !arg.startsWith('--'));

    const generator = new BootstrapGenerator();

    if (componentName) {
      generator.generateForComponent(componentName);
    } else {
      generator.generateAll();
    }

    console.log('[Bootstrap Generator] 执行成功！');
    process.exit(0);

  } catch (error) {
    console.error('[Bootstrap Generator] 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { BootstrapGenerator };
export type { ComponentInfo, ComponentConfig };
