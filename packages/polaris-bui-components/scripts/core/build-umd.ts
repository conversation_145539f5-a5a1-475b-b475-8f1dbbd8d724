#!/usr/bin/env node

/**
 * UMD 构建脚本 - 基于 material 文件夹的正确实现
 *
 * 1. 构造bootstrap文件作为入口
 * 2. 使用UMD格式打包
 * 3. Promise包装UMD文件
 * 4. 通用正则识别入口文件，middleware单独处理
 */

import { build } from 'vite';
import { existsSync, readdirSync, statSync, mkdirSync, writeFileSync, readFileSync } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { createUMDConfig } from '../../vite.umd.config';

// ES模块兼容性：获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface ComponentInfo {
  name: string;
  entry: string;
  path: string;
  type: 'base' | 'service' | 'table' | 'filter' | 'complex';
  hasBootstrap?: boolean;
  hasAppTsx?: boolean;
  hasIndexTsx?: boolean;
}

interface BuildResult {
  success: boolean;
  componentName: string;
  buildTime: number;
  outputFile?: string;
  fileSize?: number;
  cssFile?: string;
  cssSize?: number;
  error?: string;
}

class UMDBuilder {
  private componentsDir: string;
  private outputDir: string;

  constructor(
    componentsDir: string = path.join(__dirname, '../../components'),
    outputDir: string = path.join(__dirname, '../../dist')
  ) {
    this.componentsDir = componentsDir;
    this.outputDir = outputDir;

    // 确保输出目录存在
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * 扫描所有组件 - 使用通用正则识别入口文件
   */
  private scanComponents(): ComponentInfo[] {
    console.log(`[UMD Build] 扫描组件目录: ${this.componentsDir}`);

    const components: ComponentInfo[] = [];

    if (!existsSync(this.componentsDir)) {
      throw new Error(`组件目录不存在: ${this.componentsDir}`);
    }

    const entries = readdirSync(this.componentsDir);

    for (const entry of entries) {
      const componentPath = path.join(this.componentsDir, entry);
      const stat = statSync(componentPath);

      if (!stat.isDirectory()) {
        continue;
      }

      // 特殊处理base目录 - 只构建middleware组件
      if (entry === 'base') {
        console.log(`📁 扫描base目录: ${componentPath} (仅构建middleware)`);
        const middlewarePath = path.join(componentPath, 'middleware');
        if (existsSync(middlewarePath) && statSync(middlewarePath).isDirectory()) {
          const componentInfo = this.createComponentInfo('base/middleware', middlewarePath);
          if (componentInfo) {
            console.log(`📦 添加base组件: base-middleware (包含所有base子组件)`);
            components.push(componentInfo);
          }
        } else {
          console.warn(`⚠️  base/middleware 目录不存在，跳过base组件构建`);
        }
        continue;
      }

      // 处理其他组件目录
      const componentInfo = this.createComponentInfo(entry, componentPath);
      if (componentInfo) {
        components.push(componentInfo);
      }
    }

    console.log(`[UMD Build] 发现 ${components.length} 个组件:`, components.map(c => c.name));
    return components;
  }

  /**
   * 创建组件信息对象 - 优先使用bootstrap入口文件
   */
  private createComponentInfo(relativePath: string, fullPath: string): ComponentInfo | null {
    const pathParts = relativePath.split(/[\/\\]/);
    const componentName = pathParts.join('-');

    // 优先检查bootstrap入口文件
    const bootstrapEntries = [
      'bootstrap/index.ts',
      'bootstrap/index.tsx'
    ];

    let entryFile: string | null = null;
    let hasBootstrap = false;

    // 首先检查是否已有bootstrap文件
    for (const entry of bootstrapEntries) {
      const entryPath = path.join(fullPath, entry);
      if (existsSync(entryPath)) {
        entryFile = entry;
        hasBootstrap = true;
        break;
      }
    }

    // 如果没有bootstrap文件，检查其他入口文件（用于生成bootstrap）
    if (!entryFile) {
      const fallbackEntries = [
        'src/App.tsx',         // App入口
        'src/App.ts',
        'src/index.tsx',       // index入口
        'src/index.ts',
        'entry.tsx',           // 标准entry文件
        'entry.ts'
      ];

      for (const entry of fallbackEntries) {
        const entryPath = path.join(fullPath, entry);
        if (existsSync(entryPath)) {
          entryFile = entry;
          break;
        }
      }
    }

    if (!entryFile) {
      console.warn(`[UMD Build] 组件 ${componentName} 未找到入口文件，跳过`);
      return null;
    }

    // 检查是否有App.tsx和index.tsx（用于bootstrap生成）
    const hasAppTsx = existsSync(path.join(fullPath, 'src/App.tsx'));
    const hasIndexTsx = existsSync(path.join(fullPath, 'src/index.tsx'));

    // 确定组件类型
    const type = this.determineComponentType(relativePath);

    return {
      name: componentName,
      type,
      entry: path.join(fullPath, entryFile),
      path: fullPath,
      hasBootstrap,
      hasAppTsx,
      hasIndexTsx
    };
  }

  /**
   * 确定组件类型
   */
  private determineComponentType(relativePath: string): 'base' | 'service' | 'table' | 'filter' | 'complex' {
    if (relativePath.includes('base')) return 'base';
    if (relativePath.includes('service')) return 'service';
    if (relativePath.includes('table')) return 'table';
    if (relativePath.includes('filter')) return 'filter';
    return 'complex';
  }

  /**
   * 构建单个组件
   */
  private async buildComponent(component: ComponentInfo): Promise<BuildResult> {
    console.log(`[UMD Build] 开始构建组件: ${component.name}`);
    const startTime = Date.now();

    try {
      let entryPath = component.entry;

      // 如果没有bootstrap文件，则生成一个
      if (!component.hasBootstrap) {
        entryPath = await this.generateBootstrapEntry(component);
      }

      // 使用UMD配置构建组件，指定输出目录
      const config = createUMDConfig([{
        ...component,
        entry: entryPath
      }]);

      if (typeof config === 'function') {
        throw new Error('配置不能是函数形式');
      }

      // 修改配置以输出到正确位置
      const buildConfig = {
        ...config,
        build: {
          ...config.build,
          outDir: this.outputDir,
          emptyOutDir: false, // 不清空输出目录，避免覆盖之前构建的文件
        },
      };

      await build(buildConfig);

      const buildTime = Date.now() - startTime;
      const outputFile = path.join(this.outputDir, `${component.name}.umd.js`);
      const cssFile = path.join(this.outputDir, `${component.name}.css`);

      let fileSize = 0;
      let cssSize = 0;
      let cssFilePath = undefined;

      if (existsSync(outputFile)) {
        const stat = statSync(outputFile);
        fileSize = stat.size;
      } else {
        throw new Error(`构建后文件不存在: ${outputFile}`);
      }

      if (existsSync(cssFile)) {
        const stat = statSync(cssFile);
        cssSize = stat.size;
        cssFilePath = cssFile;
      }

      console.log(`[UMD Build] 组件 ${component.name} 构建成功，耗时 ${buildTime}ms，大小 ${(fileSize / 1024).toFixed(2)}KB`);

      return {
        success: true,
        componentName: component.name,
        buildTime,
        outputFile,
        fileSize,
        cssFile: cssFilePath,
        cssSize
      };
    } catch (error: any) {
      const buildTime = Date.now() - startTime;
      console.error(`[UMD Build] 构建组件 ${component.name} 失败:`, error);

      return {
        success: false,
        componentName: component.name,
        buildTime,
        error: error.message || '未知错误',
      };
    }
  }

  /**
   * 生成bootstrap入口文件 - 参考material实现
   */
  private async generateBootstrapEntry(component: ComponentInfo): Promise<string> {
    const bootstrapDir = path.join(component.path, 'bootstrap');

    // 确保bootstrap目录存在
    if (!existsSync(bootstrapDir)) {
      mkdirSync(bootstrapDir, { recursive: true });
    }

    // 确定入口文件路径
    const entryFile = component.hasAppTsx ? '../src/App' : '../src/index';

    // 确定组件类型
    const componentType = component.name;

    // 生成bootstrap内容 - 参考material的实现
    const bootstrapContent = `/**
 * Bootstrap 入口文件 - ${component.name}
 *
 * 基于 material 文件夹的正确实现
 * 包含 Amis 注册逻辑，确保组件能够正确注册到 Amis 系统中
 */

import { registerRenderer } from 'amis';
import Component from '${entryFile}';

// 组件配置信息
const componentInfo = {
  name: '${component.name}',
  type: '${componentType}',
  description: '${component.name} 组件'
};

// 注册组件到 Amis 系统
registerRenderer({
  type: componentInfo.type,
  autoVar: true,
  component: Component,
});

// 导出组件供 UMD 使用
export default Component;

// 导出组件信息
export { componentInfo };

// 确保在 UMD 环境中全局可用
if (typeof window !== 'undefined') {
  // 在 window 上设置组件引用，防止被垃圾回收
  (window as any).__BUI_COMPONENT_${component.name.replace(/[-]/g, '_')}__ = Component;

  // 输出调试信息
  console.log('[Bootstrap] ${component.name} 组件已注册');

  // 检测正确的 window 环境（支持代理 window）
  const targetWindow = window.airstarEntry?.proxyWindow || window;

  // 输出调试信息
  console.log('[Bootstrap] ${component.name} 使用 window:', targetWindow === window ? 'global' : 'proxied');

  // 调用 resolver 解锁 Promise（按照原始框架模式）
  if (typeof targetWindow['_${component.name}_resolver'] === 'function') {
    targetWindow['_${component.name}_resolver'](true);
    console.log('[Bootstrap] ${component.name} resolver 调用成功');
  } else {
    console.log('[Bootstrap] ${component.name} resolver 未找到');
  }
}`;

    // 写入文件
    const bootstrapFilePath = path.join(bootstrapDir, 'index.ts');
    writeFileSync(bootstrapFilePath, bootstrapContent);

    console.log(`[UMD Build] 生成 ${component.name} 的 bootstrap 文件: ${bootstrapFilePath}`);
    return bootstrapFilePath;
  }

  /**
   * 构建所有组件
   */
  public async buildAll(): Promise<BuildResult[]> {
    console.log(`[UMD Build] 开始 UMD 构建流程...`);

    const components = this.scanComponents();

    if (components.length === 0) {
      console.log('[UMD Build] 没有发现任何组件');
      return [];
    }

    console.log(`[UMD Build] 开始构建 ${components.length} 个组件...`);

    const results: BuildResult[] = [];

    // 顺序构建每个组件（UMD 不支持并行构建）
    for (const component of components) {
      const result = await this.buildComponent(component);
      results.push(result);
    }

    // 打印构建摘要
    this.printBuildSummary(results);

    return results;
  }

  /**
   * 打印构建摘要
   */
  private printBuildSummary(results: BuildResult[]): void {
    console.log('\n[UMD Build] 构建摘要:');
    console.log('========================================');

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`✅ 成功构建: ${successful.length} 个组件`);
    console.log(`❌ 构建失败: ${failed.length} 个组件`);

    if (successful.length > 0) {
      console.log('\n成功组件:');
      for (const result of successful) {
        const sizeKB = (result.fileSize! / 1024).toFixed(2);
        console.log(`  - ${result.componentName}: ${sizeKB}KB (${result.buildTime}ms)`);
      }
    }

    if (failed.length > 0) {
      console.log('\n失败组件:');
      for (const result of failed) {
        console.log(`  - ${result.componentName}: ${result.error}`);
      }
    }

    console.log('\n✨ 所有组件构建完成！');
  }
}

// 主函数
async function main() {
  try {
    const builder = new UMDBuilder();
    const results = await builder.buildAll();

    const hasFailures = results.some(r => !r.success);
    process.exit(hasFailures ? 1 : 0);

  } catch (error) {
    console.error('[UMD Build] 构建过程出错:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { UMDBuilder };
export type { BuildResult, ComponentInfo };
