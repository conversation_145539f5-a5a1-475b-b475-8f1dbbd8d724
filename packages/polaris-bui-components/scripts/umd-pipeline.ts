#!/usr/bin/env node

/**
 * UMD 构建管道 - polaris-bui-components
 *
 * 基于 airstar-material 的正确实现：
 * 1. 构造bootstrap文件作为入口
 * 2. 使用UMD格式打包
 * 3. Promise包装UMD文件
 * 4. 通用正则识别入口文件，middleware单独处理
 */

import { UMDBuilder } from './core/build-umd';
import { ShimGenerator } from '../utils/promise-shim';
import { existsSync, writeFileSync } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// ES模块兼容性：获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface PipelineOptions {
  components?: string[];
  skipBuild?: boolean;
  outputDir?: string;
  debug?: boolean;
  timeout?: number;
  cdnHosts?: string[];
  generatePromiseCode?: boolean;
}

interface BuildResult {
  componentName: string;
  success: boolean;
  outputFile?: string;
  fileSize?: number;
  cssFile?: string;
  cssSize?: number;
  buildTime?: number;
  error?: string;
}

interface PromiseCodeResult {
  componentName: string;
  promiseCode: string;
  syncCode: string;
  cdnUrl: string;
  cssUrl?: string;
}

class UMDPipeline {
  private options: PipelineOptions;
  private outputDir: string;
  private shimGenerator: ShimGenerator;

  constructor(options: PipelineOptions = {}) {
    this.options = {
      skipBuild: false,
      debug: false,
      timeout: 3000,
      generatePromiseCode: true,
      cdnHosts: [
        'cdnfile.corp.kuaishou.com',
        'static.yximgs.com',
        'w1.kskwai.com'
      ],
      ...options
    };

    // 设置路径
    const projectRoot = path.join(__dirname, '..');
    this.outputDir = options.outputDir || path.join(projectRoot, 'dist');

    // 初始化Promise包装器
    this.shimGenerator = new ShimGenerator({
      timeout: this.options.timeout,
      debug: this.options.debug,
      cdnHosts: this.options.cdnHosts
    });

    console.log(`[UMD Pipeline] 初始化 UMD 构建管道`);
    console.log(`[UMD Pipeline] 输出目录: ${this.outputDir}`);
    console.log(`[UMD Pipeline] 调试模式: ${this.options.debug ? '开启' : '关闭'}`);
  }

  /**
   * 执行完整的UMD构建流程
   */
  async run(): Promise<void> {
    try {
      console.log(`[UMD Pipeline] 开始执行UMD构建流程...`);

      let buildResults: BuildResult[] = [];

      // 1. 构建组件
      if (!this.options.skipBuild) {
        buildResults = await this.buildComponents();
      } else {
        console.log(`[UMD Pipeline] 跳过构建，使用现有产物`);
        buildResults = this.loadExistingBuildResults();
      }

      // 2. 生成Promise包装代码
      if (this.options.generatePromiseCode) {
        await this.generatePromiseCodes(buildResults);
      }

      // 3. 打印摘要
      this.printSummary(buildResults);

      console.log(`[UMD Pipeline] UMD构建流程完成！`);

    } catch (error) {
      console.error('[UMD Pipeline] 构建流程失败:', error);
      throw error;
    }
  }

  /**
   * 构建组件
   */
  private async buildComponents(): Promise<BuildResult[]> {
    console.log(`[UMD Pipeline] 开始构建组件...`);

    const builder = new UMDBuilder(undefined, this.outputDir);
    const results = await builder.buildAll();

    console.log(`[UMD Pipeline] 组件构建完成，成功 ${results.filter(r => r.success).length} 个，失败 ${results.filter(r => !r.success).length} 个`);

    return results;
  }

  /**
   * 加载现有构建结果
   */
  private loadExistingBuildResults(): BuildResult[] {
    // 这里可以扫描输出目录，加载现有的UMD文件
    // 简化实现，返回空数组
    console.log(`[UMD Pipeline] 加载现有构建结果...`);
    return [];
  }

  /**
   * 生成Promise包装代码
   */
  private async generatePromiseCodes(buildResults: BuildResult[]): Promise<void> {
    console.log(`[UMD Pipeline] 开始生成Promise包装代码...`);

    const promiseResults: PromiseCodeResult[] = [];

    for (const result of buildResults) {
      if (!result.success || !result.outputFile) {
        continue;
      }

      try {
        // 模拟CDN URL（实际应该从上传结果获取）
        const cdnUrl = `https://cdn.example.com/components/${result.componentName}.umd.js`;
        const cssUrl = result.cssFile ? `https://cdn.example.com/components/${result.componentName}.css` : undefined;

        // 生成Promise包装代码
        const promiseCode = this.shimGenerator.generateForComponentWithCDNFallback(
          result.componentName,
          cdnUrl,
          cssUrl
        );

        // 生成简单Promise代码
        const syncCode = this.shimGenerator.generateForComponent(
          result.componentName,
          cdnUrl,
          cssUrl
        );

        promiseResults.push({
          componentName: result.componentName,
          promiseCode,
          syncCode,
          cdnUrl,
          cssUrl
        });

        // 保存Promise代码到文件
        const promiseFilePath = path.join(this.outputDir, `${result.componentName}.promise.js`);
        const syncFilePath = path.join(this.outputDir, `${result.componentName}.sync.js`);

        writeFileSync(promiseFilePath, promiseCode);
        writeFileSync(syncFilePath, syncCode);

        console.log(`[UMD Pipeline] 生成Promise代码: ${result.componentName}`);

      } catch (error) {
        console.error(`[UMD Pipeline] 生成Promise代码失败: ${result.componentName}`, error);
      }
    }

    console.log(`[UMD Pipeline] Promise代码生成完成，共 ${promiseResults.length} 个组件`);
  }

  /**
   * 打印构建摘要
   */
  private printSummary(buildResults: BuildResult[]): void {
    console.log('\n[UMD Pipeline] 构建摘要:');
    console.log('========================================');

    const successful = buildResults.filter(r => r.success);
    const failed = buildResults.filter(r => !r.success);

    console.log(`✅ 成功构建: ${successful.length} 个组件`);
    console.log(`❌ 构建失败: ${failed.length} 个组件`);

    if (successful.length > 0) {
      console.log('\n成功组件:');
      for (const result of successful) {
        const sizeKB = result.fileSize ? (result.fileSize / 1024).toFixed(2) : 'N/A';
        const cssInfo = result.cssFile ? ` + CSS(${(result.cssSize! / 1024).toFixed(2)}KB)` : '';
        console.log(`  - ${result.componentName}: ${sizeKB}KB${cssInfo} (${result.buildTime}ms)`);
      }
    }

    if (failed.length > 0) {
      console.log('\n失败组件:');
      for (const result of failed) {
        console.log(`  - ${result.componentName}: ${result.error}`);
      }
    }

    const totalSize = successful.reduce((sum, r) => sum + (r.fileSize || 0), 0);
    const totalCssSize = successful.reduce((sum, r) => sum + (r.cssSize || 0), 0);

    console.log(`\n总文件大小: JS ${(totalSize / 1024 / 1024).toFixed(2)}MB + CSS ${(totalCssSize / 1024).toFixed(2)}KB`);
    console.log('========================================\n');
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(): PipelineOptions {
  const args = process.argv.slice(2);
  const options: PipelineOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--skip-build':
        options.skipBuild = true;
        break;
      case '--debug':
        options.debug = true;
        break;
      case '--no-promise':
        options.generatePromiseCode = false;
        break;
      case '--components':
        if (i + 1 < args.length) {
          options.components = args[++i].split(',');
        }
        break;
      case '--output-dir':
        if (i + 1 < args.length) {
          options.outputDir = args[++i];
        }
        break;
      case '--timeout':
        if (i + 1 < args.length) {
          options.timeout = parseInt(args[++i], 10);
        }
        break;
    }
  }

  return options;
}

/**
 * 主函数
 */
async function main() {
  try {
    const options = parseArgs();
    const pipeline = new UMDPipeline(options);
    await pipeline.run();

    console.log('[UMD Pipeline] 流程执行成功！');
    process.exit(0);

  } catch (error) {
    console.error('[UMD Pipeline] 流程执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { UMDPipeline };
export type { PipelineOptions, BuildResult, PromiseCodeResult };
