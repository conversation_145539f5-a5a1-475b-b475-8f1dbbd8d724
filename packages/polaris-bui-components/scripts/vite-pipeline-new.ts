#!/usr/bin/env node

/**
 * Vite Pipeline 脚本 - polaris-bui-components
 *
 * 基于 airstar-material 的实现，负责完整的构建流程：
 * 1. 调用构建器进行UMD构建
 * 2. 生成Promise包装代码
 * 3. 流程控制和参数解析
 */

import { execSync } from 'child_process';
import { existsSync, writeFileSync } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// ES模块兼容性：获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

// 导入工具
import { ShimGenerator } from '../utils/promise-shim';

interface PipelineOptions {
  skipBuild?: boolean;
  skipUpload?: boolean;
  skipApi?: boolean;
  components?: string[];
  buildMode?: 'umd';
  debug?: boolean;
  dryRun?: boolean;
  buildOnly?: boolean; // 仅构建模式
}

interface BuildResult {
  componentName: string;
  success: boolean;
  outputFile?: string;
  fileSize?: number;
  cssFile?: string;
  cssSize?: number;
  buildTime?: number;
  error?: string;
}

interface UploadResult {
  componentName: string;
  cdnUrl: string;
  cssUrl?: string;
  promiseCode: string;
  syncCode: string;
}

class VitePipeline {
  private options: PipelineOptions;
  private buildResults: BuildResult[] = [];
  private uploadResults: UploadResult[] = [];

  constructor(options: PipelineOptions = {}) {
    this.options = {
      buildMode: 'umd',
      skipBuild: false,
      skipUpload: false,
      skipApi: false,
      debug: false,
      dryRun: false,
      buildOnly: false,
      ...options
    };

    // buildOnly模式自动跳过上传和API
    if (this.options.buildOnly) {
      this.options.skipUpload = true;
      this.options.skipApi = true;
    }
  }

  /**
   * 执行构建步骤
   */
  private async runBuild(): Promise<void> {
    if (this.options.skipBuild) {
      console.log('⏭️  跳过构建步骤，加载现有构建结果...');
      this.loadExistingBuildResults();
      return;
    }

    const buildMode = this.options.buildMode || 'umd';
    console.log(`🔨 开始构建组件 (${buildMode.toUpperCase()} 模式)...`);

    if (this.options.dryRun) {
      console.log('🧪 [DRY RUN] 模拟构建过程...');
      // 模拟构建结果
      this.buildResults = [
        {
          componentName: 'base-middleware',
          success: true,
          outputFile: path.join(projectRoot, 'dist', 'base-middleware.umd.js'),
          fileSize: 112640,
          buildTime: 800
        },
        {
          componentName: 'polaris-co-table',
          success: true,
          outputFile: path.join(projectRoot, 'dist', 'polaris-co-table.umd.js'),
          fileSize: 85120,
          cssFile: path.join(projectRoot, 'dist', 'polaris-co-table.css'),
          cssSize: 4096,
          buildTime: 650
        }
      ];
      console.log(`[DRY RUN] 模拟构建完成，成功构建 ${this.buildResults.length} 个组件`);
      return;
    }

    try {
      if (buildMode === 'umd') {
        // 执行UMD构建
        const buildCommand = this.options.debug ? 'npm run build:umd:debug' : 'npm run build:umd';
        execSync(buildCommand, {
          stdio: 'inherit',
          cwd: projectRoot
        });
        this.loadUMDBuildResults();
      }

      console.log(`[Build] 构建完成，成功构建 ${this.buildResults.length} 个组件`);

    } catch (error) {
      console.error('[Build] 构建失败:', error);
      throw new Error('组件构建失败');
    }
  }

  /**
   * 加载UMD构建结果
   */
  private loadUMDBuildResults(): void {
    const distDir = path.join(projectRoot, 'dist');

    if (!existsSync(distDir)) {
      console.warn('[Build] dist目录不存在，没有构建产物');
      return;
    }

    // 这里应该扫描dist目录，加载构建结果
    // 简化实现，假设构建成功
    console.log('[Build] 加载UMD构建结果...');
  }

  /**
   * 加载现有构建结果
   */
  private loadExistingBuildResults(): void {
    // 扫描现有的构建产物
    console.log('[Build] 加载现有构建结果...');
  }

  /**
   * 生成Promise包装代码
   */
  private async generatePromiseCodes(): Promise<void> {
    if (this.options.skipUpload) {
      console.log('⏭️  跳过Promise代码生成步骤');
      return;
    }

    console.log('📦 生成Promise包装代码...');

    if (this.options.dryRun) {
      console.log('🧪 [DRY RUN] 模拟Promise代码生成...');
      for (const result of this.buildResults) {
        if (result.success) {
          this.uploadResults.push({
            componentName: result.componentName,
            cdnUrl: `https://cdn.example.com/components/${result.componentName}.umd.js`,
            cssUrl: result.cssFile ? `https://cdn.example.com/components/${result.componentName}.css` : undefined,
            promiseCode: `// Mock Promise code for ${result.componentName}`,
            syncCode: `// Mock Sync code for ${result.componentName}`
          });
        }
      }
      console.log(`[DRY RUN] 模拟Promise代码生成完成，共 ${this.uploadResults.length} 个组件`);
      return;
    }

    const shimGenerator = new ShimGenerator({
      timeout: 3000,
      debug: this.options.debug,
      cdnHosts: [
        'cdnfile.corp.kuaishou.com',
        'static.yximgs.com',
        'w1.kskwai.com'
      ]
    });

    for (const result of this.buildResults) {
      if (!result.success || !result.outputFile) {
        continue;
      }

      try {
        // 模拟CDN URL（实际应该从上传结果获取）
        const cdnUrl = `https://cdn.example.com/components/${result.componentName}.umd.js`;
        const cssUrl = result.cssFile ? `https://cdn.example.com/components/${result.componentName}.css` : undefined;

        // 生成Promise包装代码
        const promiseCode = shimGenerator.generateForComponentWithCDNFallback(
          result.componentName,
          cdnUrl,
          cssUrl
        );

        // 生成简单Promise代码
        const syncCode = shimGenerator.generateForComponent(
          result.componentName,
          cdnUrl,
          cssUrl
        );

        this.uploadResults.push({
          componentName: result.componentName,
          cdnUrl,
          cssUrl,
          promiseCode,
          syncCode
        });

        // 保存Promise代码到文件
        const distDir = path.join(projectRoot, 'dist');
        const promiseFilePath = path.join(distDir, `${result.componentName}.promise.js`);
        const syncFilePath = path.join(distDir, `${result.componentName}.sync.js`);

        writeFileSync(promiseFilePath, promiseCode);
        writeFileSync(syncFilePath, syncCode);

        console.log(`[Promise] 生成Promise代码: ${result.componentName}`);

      } catch (error) {
        console.error(`[Promise] 生成Promise代码失败: ${result.componentName}`, error);
      }
    }

    console.log(`[Promise] Promise代码生成完成，共 ${this.uploadResults.length} 个组件`);
  }

  /**
   * 模拟API调用
   */
  private async callAPI(): Promise<void> {
    if (this.options.skipApi) {
      console.log('⏭️  跳过API调用步骤');
      return;
    }

    console.log('🌐 调用后台API...');

    if (this.options.dryRun) {
      console.log('🧪 [DRY RUN] 模拟API调用...');
      console.log('[DRY RUN] 模拟API响应: 所有组件注册成功');
      return;
    }

    // 实际的API调用逻辑
    console.log('[API] 实际API调用功能待实现');
  }

  /**
   * 打印构建摘要
   */
  private printSummary(): void {
    console.log('\n📋 Pipeline 执行摘要:');
    console.log('========================================');

    const modeInfo = this.options.dryRun ? ' (DRY RUN)' : '';
    console.log(`🔧 构建模式: ${this.options.buildMode?.toUpperCase()}${modeInfo}`);

    if (this.options.buildOnly) {
      console.log('📦 执行模式: 仅构建');
    } else if (this.options.skipUpload && this.options.skipApi) {
      console.log('📦 执行模式: 仅构建');
    } else if (this.options.skipApi) {
      console.log('📦 执行模式: 构建 + Promise生成');
    } else {
      console.log('📦 执行模式: 完整流程');
    }

    const successful = this.buildResults.filter(r => r.success);
    const failed = this.buildResults.filter(r => !r.success);

    console.log(`✅ 成功构建: ${successful.length} 个组件`);
    console.log(`❌ 构建失败: ${failed.length} 个组件`);

    if (successful.length > 0) {
      console.log('\n成功组件:');
      for (const result of successful) {
        const sizeKB = result.fileSize ? (result.fileSize / 1024).toFixed(2) : 'N/A';
        const cssInfo = result.cssFile ? ` + CSS(${(result.cssSize! / 1024).toFixed(2)}KB)` : '';
        console.log(`  - ${result.componentName}: ${sizeKB}KB${cssInfo} (${result.buildTime}ms)`);
      }
    }

    if (failed.length > 0) {
      console.log('\n失败组件:');
      for (const result of failed) {
        console.log(`  - ${result.componentName}: ${result.error}`);
      }
    }

    if (this.uploadResults.length > 0) {
      console.log(`\n📦 Promise代码: ${this.uploadResults.length} 个组件`);
    }

    console.log('========================================\n');
  }

  /**
   * 执行完整流程
   */
  async run(): Promise<void> {
    console.log('🚀 开始Vite Pipeline流程...');
    console.log(`[Pipeline] 构建模式: ${this.options.buildMode?.toUpperCase() || 'UMD'}`);

    if (this.options.dryRun) {
      console.log(`[Pipeline] 🧪 干跑模式启用 - 所有操作将被模拟`);
    }

    if (this.options.buildOnly) {
      console.log(`[Pipeline] 📦 仅构建模式 - 跳过上传和API调用`);
    }

    try {
      // 步骤1: 构建
      await this.runBuild();

      // 步骤2: 生成Promise包装代码
      await this.generatePromiseCodes();

      // 步骤3: 调用API
      await this.callAPI();

      // 输出总结
      this.printSummary();

      console.log('✨ Pipeline 流程完成！');

    } catch (error) {
      console.error('❌ Pipeline 流程失败:', error);
      throw error;
    }
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(): PipelineOptions {
  const args = process.argv.slice(2);
  const options: PipelineOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--skip-build':
        options.skipBuild = true;
        break;
      case '--skip-upload':
        options.skipUpload = true;
        break;
      case '--skip-api':
        options.skipApi = true;
        break;
      case '--debug':
        options.debug = true;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--build-only':
        options.buildOnly = true;
        break;
      case '--build-mode=umd':
        options.buildMode = 'umd';
        break;
      case '--components':
        if (i + 1 < args.length) {
          options.components = args[++i].split(',');
        }
        break;
    }
  }

  return options;
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
🚀 Polaris BUI Components Pipeline

用法:
  pnpm run <command> [options]

命令:
  pipeline                完整流程 (构建 + Promise生成 + API调用)
  pipeline:dry-run        干跑模式 (测试所有步骤，不实际执行)
  pipeline:build-only     仅构建模式 (只构建，不上传不调用API)
  pipeline:skip-build     跳过构建 (使用现有构建产物)
  pipeline:skip-upload    跳过上传 (仅构建，不生成Promise代码)
  pipeline:skip-api       跳过API (构建 + Promise生成，不调用API)

测试命令:
  test:build              测试构建流程 (dry-run + debug)
  test:upload             测试上传流程 (跳过API + debug)
  test:api                测试API流程 (跳过构建和上传 + debug)

选项:
  --skip-build           跳过构建步骤
  --skip-upload          跳过上传步骤
  --skip-api             跳过API调用步骤
  --dry-run              干跑模式，模拟所有操作
  --debug                开启调试模式
  --build-mode=umd       指定构建模式 (默认: umd)
  --components=comp1,comp2  只处理指定组件

示例:
  pnpm run pipeline                    # 完整流程
  pnpm run pipeline:dry-run            # 测试所有步骤
  pnpm run pipeline:build-only         # 仅构建
  pnpm run test:build                  # 测试构建
  pnpm run pipeline -- --debug        # 开启调试模式
  `);
}

/**
 * 主函数
 */
async function main() {
  try {
    const args = process.argv.slice(2);

    // 显示帮助信息
    if (args.includes('--help') || args.includes('-h')) {
      showHelp();
      process.exit(0);
    }

    const options = parseArgs();
    const pipeline = new VitePipeline(options);
    await pipeline.run();

    console.log('[Pipeline] 流程执行成功！');
    process.exit(0);

  } catch (error) {
    console.error('[Pipeline] 流程执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { VitePipeline };
export type { PipelineOptions, BuildResult, UploadResult };
