curl --location 'https://airstar.corp.kuaishou.com/api/asset/batchSaveOrCreateCodeForPipeline' \
--header 'trace-context: {"laneId": "error_test"}' \
--header 'Cookie: pa-gateway-token=e318067c-929c-4ef9-b7f7-3cc87324d97b; amc-token-host=airstar-amc.corp.kuaishou.com; apdid=6aa14fdc-1421-411c-9580-75eda4dacb6da032a1285f6735d3ecf295f4f90649b7:1753077922:1; did=web_f744280ca601743363b00c2ab3d22039699f; hdige2wqwoino=SJcdtpRyBXdz5w76FraknAtntasAefZM8596172e; soft_did=1619580708547; Airstar-userInfo-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySW5mbyI6eyJuYW1lIjoiZGVuZ3lvdW5hbiIsImRpc3BsYXlOYW1lIjoi6YKT5oKg5Y2XIiwibWFpbCI6ImRlbmd5b3VuYW5Aa3VhaXNob3UuY29tIiwiYXZhdGFyIjoiaHR0cHM6Ly9zdGF0aWMueXhpbWdzLmNvbS91ZGF0YS9wa2cvS1MtSVMtQVZBVEFSLVBST0RVQ1RJT04vcHJlLWUzNDk3MTg2NjI3OTQwMzdiMmRiYWJiNzE0NjYzMGVlLzQ0NDA3MkNEODYwNTBDNzgxNUQwNzI5QjM5QTEzMkRCX2NvbXByZXNzZWRfMTAwLmpwZyIsImRlcGFydG1lbnQiOiLkuLvnq5nmioDmnK_pg6giLCJuYW1lWmgiOiLpgpPmgqDljZcifSwiaWF0IjoxNzUzMDgzNzU3fQ.TJiSKTFYkA7evxfhZIyer0X5shiiCdF5UIBLGIFUMeU; polaris_tk_prod=NELZBf582NTbWDF+KcYL/EfS1pawjsXswbEQ8PenuBM1AMGKFtf2XCLImRGzgDVxAxQh5q5KHecK7fTy1Oj7GhOZNU6vXwMX0P0pcolTaa8jf2b5/VZ9b8JzZV6Qx16W; weblogger_did=web_138842851576B388; bUserId=1000255816241; userId=72206456; kuaishou.shop.ideat_st=ChZrdWFpc2hvdS5zaG9wLmlkZWF0LnN0EsAB3LN9HWmEOyjjlEzKg8YfVJ1hylEmDpfnyd9Dnq4FPARxrMcw2GzRCjInqLzuITsOxDRFyO03ZBxKSXF96K4juwpQ_s9Sn2ZcMQaN2MmeYczE1ez-AMsJlQY_5oovLmKN4VMCSAUlh4d0XjqvwymerEqndgN9TKHJCdKxC_YJ_gxf0lQApt6WTgCc3iC2EBmcx5B6bVq-aKuq3vcV6kTvoXdbvy2tFT39ggS1qZLtKDl7jp4l6OmRrzMP4EixH05uGhJ1qtbF_gkmYl7CzKhbuG3g1nYiIBjPIe2orEKO8EIYFIh5MV7fw66DUI1kGLztTR3MCmTgKAUwAQ; kuaishou.shop.ideat_ph=a770ecf691f5c70fe421f1c8228d3a51b541; ehid=6500cwRKJZ8ZbZlb04sd2q8Fsmx2GBUmaiYM9; accessproxy_session=2d6a4129-c5d0-4c61-8bff-ce0dbef568ba; ktrace-context=1|MS43MzY3Njk0NTcyODQ3MTY3LjQ2NjU0MTYxLjE3NTMxNTQzNzIxNDQuMTQ1NzYwOQ==|MS43MzY3Njk0NTcyODQ3MTY3LjQ1NTMzNjQzLjE3NTMxNTQzNzIxNDQuMTQ1NzYxMA==|0|webservice-htgc|operation|true|src-Js; apdid=e04a1c0e-5808-4f68-83b6-0016d5649fb4de0c55df68c372384440c8f070001b6e:1745554667:1; accessproxy_session=204193f4-f5d0-49e3-8341-dac7f03ebee7' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyTmFtZSI6ImRlbmd5b3VuYW4iLCJpYXQiOjE3NTE2MzM2MzZ9.m69O-qVhExsSpqcwpFQ5AQQpIUyIMuwa876wpnGCYE8' \
--header 'Content-Type: application/json' \
--data '{
    "appId": "624425b5327587ef7f317fd2",
    "components": "[{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p2-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-author-flow-service-Z_JmYE82/main.js\",\"currentVersionNo\":87,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-author-flow-service in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-author-flow-service_Lock'\''; const eventName = '\''_polaris-author-flow-service_LockReleased'\''; const finalStatusFlag = '\''_polaris-author-flow-service_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p4-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-author-flow-service-Z_JmYE82-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-author-flow-service consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-author-flow-service Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-author-flow-service'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-author-flow-service-Z_JmYE82_resolver'\''] = resolveWrapper; topWindow['\''_polaris-author-flow-service-Z_JmYE82_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-author-flow-service load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-author-flow-service Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-author-flow-service resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-author-flow-service acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-author-flow-service 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-author-flow-service\",\"nameZh\":\"流量塑形容器组件\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-complex-filter-v1-_DNnPsJH/main.js\",\"currentVersionNo\":81,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-complex-filter-v1 in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-complex-filter-v1_Lock'\''; const eventName = '\''_polaris-complex-filter-v1_LockReleased'\''; const finalStatusFlag = '\''_polaris-complex-filter-v1_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-complex-filter-v1-_DNnPsJH-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-complex-filter-v1 consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-complex-filter-v1 Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-complex-filter-v1'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-complex-filter-v1-_DNnPsJH_resolver'\''] = resolveWrapper; topWindow['\''_polaris-complex-filter-v1-_DNnPsJH_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-complex-filter-v1 load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-complex-filter-v1 Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-complex-filter-v1 resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-complex-filter-v1 acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-complex-filter-v1 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-complex-filter-v1\",\"nameZh\":\"polaris-complex-filter-v1\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-co-table-author-flow-4t5kBrCn/main.js\",\"currentVersionNo\":104,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-co-table-author-flow in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-co-table-author-flow_Lock'\''; const eventName = '\''_polaris-co-table-author-flow_LockReleased'\''; const finalStatusFlag = '\''_polaris-co-table-author-flow_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-co-table-author-flow-4t5kBrCn-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-co-table-author-flow consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-co-table-author-flow Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-co-table-author-flow'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-co-table-author-flow-4t5kBrCn_resolver'\''] = resolveWrapper; topWindow['\''_polaris-co-table-author-flow-4t5kBrCn_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-co-table-author-flow load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-co-table-author-flow Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-co-table-author-flow resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-co-table-author-flow acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-co-table-author-flow 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-co-table-author-flow\",\"nameZh\":\"璇玑通用表格组件\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-easy-form-filter-author-flow-Dt5drczB/main.js\",\"currentVersionNo\":104,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-easy-form-filter-author-flow in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-easy-form-filter-author-flow_Lock'\''; const eventName = '\''_polaris-easy-form-filter-author-flow_LockReleased'\''; const finalStatusFlag = '\''_polaris-easy-form-filter-author-flow_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p2-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-easy-form-filter-author-flow-Dt5drczB-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-easy-form-filter-author-flow consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-easy-form-filter-author-flow Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-easy-form-filter-author-flow'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-easy-form-filter-author-flow-Dt5drczB_resolver'\''] = resolveWrapper; topWindow['\''_polaris-easy-form-filter-author-flow-Dt5drczB_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-easy-form-filter-author-flow load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-easy-form-filter-author-flow Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-easy-form-filter-author-flow resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-easy-form-filter-author-flow acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-easy-form-filter-author-flow 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-easy-form-filter-author-flow\",\"nameZh\":\"简易form表单组件\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-ks-table-wQwU0tEY/main.js\",\"currentVersionNo\":80,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-ks-table in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-ks-table_Lock'\''; const eventName = '\''_polaris-ks-table_LockReleased'\''; const finalStatusFlag = '\''_polaris-ks-table_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-ks-table-wQwU0tEY-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-ks-table consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-ks-table Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-ks-table'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-ks-table-wQwU0tEY_resolver'\''] = resolveWrapper; topWindow['\''_polaris-ks-table-wQwU0tEY_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-ks-table load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-ks-table Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-ks-table resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-ks-table acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-ks-table 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-ks-table\",\"nameZh\":\"polaris-ks-table\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p2-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-middleware-Unmn-jLZ/main.js\",\"currentVersionNo\":75,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-middleware in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-middleware_Lock'\''; const eventName = '\''_polaris-middleware_LockReleased'\''; const finalStatusFlag = '\''_polaris-middleware_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-middleware-Unmn-jLZ-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-middleware consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-middleware Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-middleware'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-middleware-Unmn-jLZ_resolver'\''] = resolveWrapper; topWindow['\''_polaris-middleware-Unmn-jLZ_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-middleware load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-middleware Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-middleware resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-middleware acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-middleware 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-middleware\",\"nameZh\":\"polaris-middleware\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p4-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-service-insight-content-realtime-JhL9Gohn/main.js\",\"currentVersionNo\":81,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-service-insight-content-realtime in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-service-insight-content-realtime_Lock'\''; const eventName = '\''_polaris-service-insight-content-realtime_LockReleased'\''; const finalStatusFlag = '\''_polaris-service-insight-content-realtime_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-service-insight-content-realtime-JhL9Gohn-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-service-insight-content-realtime consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-service-insight-content-realtime Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-service-insight-content-realtime'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-service-insight-content-realtime-JhL9Gohn_resolver'\''] = resolveWrapper; topWindow['\''_polaris-service-insight-content-realtime-JhL9Gohn_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-service-insight-content-realtime load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-service-insight-content-realtime Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutIad); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-service-insight-content-realtime resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-service-insight-content-realtime acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-service-insight-content-realtime 执行垫片逻时报错'\'', error); reject(error); } });\",\"name\":\"polaris-service-insight-content-realtime\",\"nameZh\":\"polaris-service-insight-content-realtime\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-service-realtime-video-ranking-eRARB5wm/main.js\",\"currentVersionNo\":21,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-service-realtime-video-ranking in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-service-realtime-video-ranking_Lock'\''; const eventName = '\''_polaris-service-realtime-video-ranking_LockReleased'\''; const finalStatusFlag = '\''_polaris-service-realtime-video-ranking_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-service-realtime-video-ranking-eRARB5wm-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-service-realtime-video-ranking consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-service-realtime-video-ranking Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-service-realtime-video-ranking'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-service-realtime-video-ranking-eRARB5wm_resolver'\''] = resolveWrapper; topWindow['\''_polaris-service-realtime-video-ranking-eRARB5wm_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-service-realtime-video-ranking load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-service-realtime-video-ranking Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-service-realtime-video-ranking resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-service-realtime-video-ranking acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-service-realtime-video-ranking 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-service-realtime-video-ranking\",\"nameZh\":\"实时视频榜单服务组件\"},{\"appId\":\"624425b5327587ef7f317fd2\",\"code\":\"https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-tabs-author-flow-kqq0UjVc/main.js\",\"currentVersionNo\":104,\"syncCode\":\"new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('\''[lux-info]polaris-tabs-author-flow in iframe'\''); topWindow = topWindow.parent; } const lockName = '\''_polaris-tabs-author-flow_Lock'\''; const eventName = '\''_polaris-tabs-author-flow_LockReleased'\''; const finalStatusFlag = '\''_polaris-tabs-author-flow_FinalStatus'\''; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === '\''success'\'') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('\''script'\''); script.src = '\''https://p2-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-tabs-author-flow-kqq0UjVc-sync/main.js'\''; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('\''[lux-info]polaris-tabs-author-flow consumed'\'', perfLoss, '\''ms to finish load, timeout:'\'', timeout); resolved = true; if (timeout) { releaseLock('\''failed'\''); reject('\''[lux-info]polaris-tabs-author-flow Load timeout'\''); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: '\''airstar-load-custom-components-per-loss'\'', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '\''polaris-tabs-author-flow'\'', perfLoss, }, }); releaseLock('\''success'\''); delete topWindow[lockName]; resolve(); } }; window['\''_polaris-tabs-author-flow-kqq0UjVc_resolver'\''] = resolveWrapper; topWindow['\''_polaris-tabs-author-flow-kqq0UjVc_resolver'\''] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('\''[lux-info]polaris-tabs-author-flow load failed'\'', e); clearTimeout(timeoutId); releaseLock('\''failed'\''); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('\''[lux-info]polaris-tabs-author-flow Another instance is already running, waiting for lock release.'\''); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === '\''success'\'') { console.log('\''[lux-info]polaris-tabs-author-flow resolve directly'\''); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('\''[lux-info]polaris-tabs-author-flow acquire lock again'\''); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('\''[lux-info]polaris-tabs-author-flow 执行垫片逻辑时报错'\'', error); reject(error); } });\",\"name\":\"polaris-tabs-author-flow\",\"nameZh\":\"璇玑标签选择器\"}]",
    "pages": "[]",
    "isMasterBranch": "true",
    "laneId": "feat_p2p_ai_0625"
}'