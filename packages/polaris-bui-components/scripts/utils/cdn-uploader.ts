/**
 * CDN上传工具 - 用于上传组件文件到CDN
 * 基于airstar-material的real-cdn-uploader实现，适配polaris-bui-components
 */

import { existsSync, statSync, createReadStream } from 'node:fs';
import * as path from 'node:path';
import fetch from 'node-fetch';

// 使用 form-data 包来创建 FormData（兼容现有环境）
import FormDataNode from 'form-data';

interface UploadOptions {
  filePath: string;
  componentName: string;
  buildHash?: string;
  external?: boolean;
}

interface UploadResult {
  success: boolean;
  componentName: string;
  cdnUrl?: string;
  buildHash?: string;
  error?: string;
  provider?: string;
  uploadTime?: number;
  fileSize?: number;
}

interface UploadStats {
  totalFiles: number;
  successCount: number;
  failureCount: number;
  totalSize: number;
  avgUploadTime: number;
}

export class PolarisUMDUploader {
  private provider: 'mock' | 'real';

  constructor(provider: 'mock' | 'real' = 'real') {
    this.provider = provider;
  }

  /**
   * 批量上传文件
   */
  async uploadBatch(uploadOptions: UploadOptions[]): Promise<UploadResult[]> {
    const results: UploadResult[] = [];

    console.log(`[CDN] 开始批量上传 ${uploadOptions.length} 个文件...`);

    for (const options of uploadOptions) {
      const result = await this.uploadFile(options);
      results.push(result);

      // 短暂延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    return results;
  }

  /**
   * 上传单个文件
   */
  async uploadFile(options: UploadOptions): Promise<UploadResult> {
    const startTime = Date.now();
    
    try {
      // 获取文件信息
      const stats = statSync(options.filePath);
      const fileSize = stats.size;
      
      // 生成上传路径
      const buildHash = options.buildHash || this.generateBuildHash();
      const fileName = path.basename(options.filePath);
      
      console.log(`[CDN] 开始上传: ${options.componentName}/${fileName} (${(fileSize / 1024).toFixed(2)}KB)`);
      
      let cdnUrl: string;
      
      if (this.provider === 'mock') {
        // 模拟上传
        cdnUrl = await this.mockUpload(options.componentName, buildHash, fileName, fileSize);
      } else {
        // 真实上传
        cdnUrl = await this.realUpload(options.filePath, options.componentName, buildHash);
      }
      
      const uploadTime = Date.now() - startTime;
      
      console.log(`[CDN] ✅ 上传成功: ${cdnUrl} (${uploadTime}ms)`);
      
      return {
        success: true,
        componentName: options.componentName,
        cdnUrl,
        buildHash,
        provider: this.provider,
        uploadTime,
        fileSize,
      };
      
    } catch (error) {
      const uploadTime = Date.now() - startTime;
      console.error(`[CDN] ❌ 上传失败 (${uploadTime}ms):`, error);
      
      return {
        success: false,
        componentName: options.componentName,
        error: error.message,
        provider: this.provider,
        uploadTime,
        fileSize: 0,
      };
    }
  }

  /**
   * 模拟上传
   */
  private async mockUpload(
    componentName: string, 
    buildHash: string, 
    fileName: string,
    fileSize: number
  ): Promise<string> {
    // 模拟网络延迟
    const delay = 800 + Math.random() * 1200; // 0.8-2.0秒
    await new Promise(resolve => setTimeout(resolve, delay));

    // 模拟偶尔的上传失败
    if (Math.random() < 0.05) { // 5% 失败率
      throw new Error('模拟上传失败');
    }

    // 生成符合curl示例格式的模拟CDN URL
    // 参考格式: https://p1-live.wskwai.com/kos/nlav12127/polaris-configuration-material-polaris-co-table-author-flow-4t5kBrCn/main.js
    const mockDomains = [
      'p1-live.wskwai.com',
      'p2-live.wskwai.com', 
      'p3-live.wskwai.com',
      'p4-live.wskwai.com'
    ];
    const mockDomain = mockDomains[Math.floor(Math.random() * mockDomains.length)];
    const mockPath = `kos/nlav12127/polaris-bui-components-${componentName}-${buildHash}`;
    return `https://${mockDomain}/${mockPath}/main.js`;
  }

  /**
   * 构建FormData（参考 real-cdn-uploader 实现）
   */
  private buildFormData(filePath: string, componentName: string, buildHash: string): FormDataNode {
    const formData = new FormDataNode();

    // 构建目录名称（与 real-cdn-uploader 保持一致）
    const dirName = `polaris-bui-components-${componentName}-${buildHash}`;
    formData.append('dir', dirName);

    // 添加文件（使用文件流，与 real-cdn-uploader 保持一致）
    try {
      const fileStream = createReadStream(filePath);
      const fileName = path.basename(filePath);

      // 转换文件名：UMD -> CJS
      const uploadFileName = fileName.includes('.umd.')
        ? fileName.replace('.umd.js', '.cjs.js')
        : `${componentName}.cjs.js`;

      formData.append('files', fileStream, {
        filename: uploadFileName,
        contentType: 'application/javascript'
      });

      console.log(`[CDN] 准备上传文件: ${fileName} -> ${uploadFileName} 到目录: ${dirName}`);

    } catch (error) {
      throw new Error(`文件读取失败: ${error.message}`);
    }

    return formData;
  }

  /**
   * 真实上传到CDN（参考 real-cdn-uploader 实现）
   */
  private async realUpload(
    filePath: string,
    componentName: string,
    buildHash: string
  ): Promise<string> {
    const maxRetries = 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.performUpload(filePath, componentName, buildHash);
      } catch (error) {
        lastError = error as Error;
        console.log(`[CDN] ⚠️ 上传失败，准备重试 (${attempt}/${maxRetries}): ${componentName} ${error.message}`);

        if (attempt < maxRetries) {
          // 指数退避：第一次重试等待1秒，第二次等待2秒
          const delay = attempt * 1000;
          console.log(`[CDN] 等待 ${delay}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(`❌ 上传失败，已重试${maxRetries}次: ${componentName} ${lastError?.message}`);
  }

  /**
   * 执行单次上传（参考 real-cdn-uploader 实现）
   */
  private async performUpload(
    filePath: string,
    componentName: string,
    buildHash: string
  ): Promise<string> {
    // 验证文件存在
    if (!existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    const stats = statSync(filePath);
    const fileSize = stats.size;
    const fileName = path.basename(filePath);

    console.log(`[CDN] 开始上传: ${componentName}/${fileName} (${(fileSize / 1024).toFixed(2)}KB)`);

    // 构建FormData
    const formData = this.buildFormData(filePath, componentName, buildHash);

    // 上传文件
    const fetchData = await this.uploadFiles(formData);

    // 处理响应
    const cdnUrl = this.handleUploadResponse(fetchData, fileName);

    if (!cdnUrl) {
      throw new Error('CDN URL获取失败');
    }

    return cdnUrl;
  }

  /**
   * 上传文件到CDN（参考 real-cdn-uploader 实现）
   */
  private async uploadFiles(formData: FormDataNode): Promise<any> {
    const uploadURL = 'https://airstar.corp.kuaishou.com/api/upload/batchUploadFiles';

    console.log(`[CDN] 上传到: ${uploadURL}`);

    try {
      const response = await fetch(uploadURL, {
        method: 'POST',
        body: formData as any,
        // 不手动设置 headers，让 form-data 自动处理
      });

      if (!response.ok) {
        // 获取详细的错误信息
        let errorText = '';
        try {
          errorText = await response.text();
          console.error(`[CDN] HTTP错误响应:`, errorText);
        } catch (e) {
          console.error(`[CDN] 无法读取错误响应`);
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`);
      }

      const result = await response.json();
      console.log(`[CDN] 服务器响应:`, JSON.stringify(result, null, 2));
      return result;

    } catch (error) {
      console.error(`[CDN] 上传请求失败:`, error);
      // 增强错误信息
      if (error.name === 'AbortError') {
        throw new Error(`上传超时`);
      } else if (error.code === 'ECONNRESET') {
        throw new Error(`连接被重置，可能是网络问题或服务器负载过高`);
      } else if (error.code === 'ENOTFOUND') {
        throw new Error(`DNS解析失败，请检查网络连接`);
      }
      throw error;
    }
  }

  /**
   * 处理上传响应（参考 real-cdn-uploader 实现）
   */
  private handleUploadResponse(fetchData: any, originalFileName: string): string {
    console.log(`[CDN] 处理上传响应:`, JSON.stringify(fetchData, null, 2));

    // 计算期望的上传文件名
    const uploadFileName = originalFileName.includes('.umd.')
      ? originalFileName.replace('.umd.js', '.cjs.js')
      : originalFileName.replace('.js', '.cjs.js');

    if (fetchData.errorCode === 0 && fetchData.data && fetchData.data.fileResults) {
      const mainFile = fetchData.data.fileResults.find((item: { cdnUrl: string }) =>
        item.cdnUrl.endsWith(uploadFileName) || item.cdnUrl.includes(uploadFileName.replace('.cjs.js', ''))
      );

      if (mainFile) {
        // 处理CDN URL替换（如 real-cdn-uploader 中的逻辑）
        let cdnUrl = mainFile.cdnUrl;
        if (cdnUrl.includes('var----h5----var.kskwai.com')) {
          cdnUrl = cdnUrl.replace('https://var----h5----var.kskwai.com', 'https://w1.kskwai.com');
        }

        console.log(`[CDN] ✅ 上传成功: ${cdnUrl}`);
        return cdnUrl;
      } else {
        console.error(`[CDN] 在响应中未找到文件: ${uploadFileName}`);
        console.error(`[CDN] 可用文件:`, fetchData.data.fileResults.map((f: any) => f.cdnUrl));
      }
    }

    console.error(`[CDN] ❌ 上传失败:`, JSON.stringify(fetchData));
    return '';
  }

  /**
   * 生成构建哈希
   */
  private generateBuildHash(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  }

  /**
   * 获取上传统计信息（参考 real-cdn-uploader 实现）
   */
  getUploadStats(results: UploadResult[]): UploadStats {
    const totalFiles = results.length;
    const successCount = results.filter(r => r.success).length;
    const failureCount = totalFiles - successCount;
    const totalSize = results.reduce((sum, r) => sum + (r.fileSize || 0), 0);
    const avgUploadTime = results.reduce((sum, r) => sum + (r.uploadTime || 0), 0) / totalFiles;

    return {
      totalFiles,
      successCount,
      failureCount,
      totalSize,
      avgUploadTime,
    };
  }


}

/**
 * 创建CDN上传器的便捷函数
 */
export function createPolarisUMDUploader(useMock: boolean = false): PolarisUMDUploader {
  const provider = useMock ? 'mock' : 'real';
  console.log(`[CDN] 创建${provider === 'mock' ? '模拟' : '真实'}CDN上传器`);
  return new PolarisUMDUploader(provider);
} 