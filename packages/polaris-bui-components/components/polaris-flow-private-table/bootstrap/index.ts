/**
 * Bootstrap 入口文件 - polaris-flow-private-table
 *
 * 基于 material 文件夹的正确实现
 * 包含 Amis 注册逻辑，确保组件能够正确注册到 Amis 系统中
 */

import { registerRenderer } from 'amis';
import Component from '../entry';

// 组件配置信息
const componentInfo = {
  name: 'polaris-flow-private-table',
  type: 'polaris-flow-private-table',
  description: 'polaris-flow-private-table 组件',
  assetId: '',
  appId: '',
};

// 注册组件到 Amis 系统
registerRenderer({
  type: componentInfo.type,
  autoVar: true,
  component: Component,
});

// 导出组件供 UMD 使用
export default Component;

// 导出组件信息
export { componentInfo };

// 确保在 UMD 环境中全局可用
if (typeof window !== 'undefined') {
  // 在 window 上设置组件引用，防止被垃圾回收
  (window as any).__BUI_COMPONENT_polaris_flow_private_table__ = Component;

  // 输出调试信息
  console.log('[Bootstrap] polaris-flow-private-table 组件已注册');

  // 检测正确的 window 环境（支持代理 window）
  const targetWindow = window.airstarEntry?.proxyWindow || window;

  // 输出调试信息
  console.log('[Bootstrap] polaris-flow-private-table 使用 window:', targetWindow === window ? 'global' : 'proxied');

  // 调用 resolver 解锁 Promise（按照原始框架模式）
  if (typeof targetWindow['_polaris-flow-private-table_resolver'] === 'function') {
    targetWindow['_polaris-flow-private-table_resolver'](true);
    console.log('[Bootstrap] polaris-flow-private-table resolver 调用成功');
  } else {
    console.log('[Bootstrap] polaris-flow-private-table resolver 未找到');
  }
}