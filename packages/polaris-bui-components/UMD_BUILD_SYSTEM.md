# UMD 构建系统 - polaris-bui-components

基于 airstar-material 文件夹的正确实现，重写了 polaris-bui-components 的打包逻辑。

## 核心改进

### 1. Bootstrap 入口文件
- **不再逐个识别 entry 文件**，而是构造 bootstrap 文件作为入口
- Bootstrap 文件包含正确的 `registerRenderer` 调用
- 自动生成 Promise resolver 逻辑

### 2. UMD 格式打包
- **使用 UMD 格式**，不再是 CJS
- 每个组件生成独立的 `.umd.js` 文件
- 支持浏览器直接使用

### 3. Promise 包装
- **UMD 文件包裹在 Promise 中**进行传输
- 支持超时、重试、CDN 回退机制
- 生成两种代码：Promise 版本和同步版本

### 4. 通用正则识别
- **使用通用正则语句**读取入口文件
- 支持多种入口文件格式：`src/App.tsx`、`src/index.tsx`、`entry.tsx` 等
- **middleware 单独列出**，特殊处理 base 目录

## 文件结构

```
packages/polaris-bui-components/
├── scripts/
│   ├── core/
│   │   ├── build-umd.ts          # UMD 构建器
│   │   └── generate-bootstrap.ts  # Bootstrap 生成器
│   └── umd-pipeline.ts           # UMD 构建管道
├── utils/
│   └── promise-shim.ts           # Promise 包装器
└── dist/                         # 构建产物
    ├── *.umd.js                  # UMD 组件文件
    ├── *.css                     # CSS 样式文件
    ├── *.promise.js              # Promise 包装代码
    └── *.sync.js                 # 同步加载代码
```

## 使用方式

### 构建所有组件
```bash
pnpm run build:umd
```

### 调试模式构建
```bash
pnpm run build:umd:debug
```

### 跳过构建，使用现有产物
```bash
pnpm run build:umd:skip
```

### 生成 Bootstrap 文件
```bash
pnpm run generate:bootstrap
```

### 为指定组件生成 Bootstrap
```bash
pnpm run generate:bootstrap -- component-name
```

## 组件扫描逻辑

### 入口文件优先级
1. `bootstrap/index.ts` (最高优先级)
2. `bootstrap/index.tsx`
3. `entry.tsx` (标准 entry 文件)
4. `entry.ts`
5. `src/App.tsx` (App 入口)
6. `src/App.ts`
7. `src/index.tsx` (index 入口)
8. `src/index.ts`

### 特殊处理
- **base 目录**：只构建 `base/middleware` 组件
- **其他目录**：使用通用正则识别所有组件

### 组件类型识别
- `base`: 基础组件
- `service`: 服务组件
- `table`: 表格组件
- `filter`: 过滤器组件
- `complex`: 复杂组件

## Bootstrap 文件生成

每个组件会自动生成 `bootstrap/index.ts` 文件：

```typescript
/**
 * Bootstrap 入口文件 - component-name
 */

import { registerRenderer } from 'amis';
import Component from '../src/App'; // 或 ../src/index

// 组件配置信息
const componentInfo = {
  name: 'component-name',
  type: 'component-name',
  description: 'component-name 组件'
};

// 注册组件到 Amis 系统
registerRenderer({
  type: componentInfo.type,
  autoVar: true,
  component: Component,
});

// 导出组件供 UMD 使用
export default Component;

// UMD 环境中的全局可用性
if (typeof window !== 'undefined') {
  // 设置组件引用，防止被垃圾回收
  (window as any).__BUI_COMPONENT_component_name__ = Component;
  
  // 检测代理 window 环境
  const targetWindow = window.airstarEntry?.proxyWindow || window;
  
  // 调用 resolver 解锁 Promise
  if (typeof targetWindow['_component-name_resolver'] === 'function') {
    targetWindow['_component-name_resolver'](true);
  }
}
```

## Promise 包装代码

为每个组件生成两种 Promise 代码：

### 1. 带 CDN 回退的 Promise 代码 (*.promise.js)
```javascript
// Promise 包装代码 with CDN fallback for component-name
(function() {
  const componentName = 'component-name';
  const primaryUrl = 'https://cdn.example.com/components/component-name.umd.js';
  const fallbackUrls = [
    'https://cdnfile.corp.kuaishou.com/components/component-name.umd.js',
    'https://static.yximgs.com/components/component-name.umd.js'
  ];
  
  // 锁机制，防止重复加载
  // Promise 创建和超时处理
  // CDN 回退逻辑
  // ...
})()
```

### 2. 简单 Promise 代码 (*.sync.js)
```javascript
// Promise 包装代码 for component-name
(function() {
  const componentName = 'component-name';
  const cdnUrl = 'https://cdn.example.com/components/component-name.umd.js';
  
  // 简单的 Promise 包装
  // ...
})()
```

## 配置选项

### UMD Pipeline 选项
- `--skip-build`: 跳过构建，使用现有产物
- `--debug`: 开启调试模式
- `--no-promise`: 不生成 Promise 包装代码
- `--components component1,component2`: 只构建指定组件
- `--output-dir path`: 指定输出目录
- `--timeout 5000`: 设置超时时间（毫秒）

### Promise 包装选项
- `timeout`: 加载超时时间（默认 3000ms）
- `cdnHosts`: CDN 回退主机列表
- `debug`: 调试模式开关

## 与 material 文件夹的对比

| 特性 | material 实现 | bui 新实现 |
|------|---------------|------------|
| 入口文件 | bootstrap 优先 | ✅ bootstrap 优先 |
| 打包格式 | UMD | ✅ UMD |
| Promise 包装 | ✅ 支持 | ✅ 支持 |
| 通用正则 | ✅ 支持 | ✅ 支持 |
| middleware 处理 | ✅ 单独列出 | ✅ 单独列出 |
| CDN 回退 | ✅ 支持 | ✅ 支持 |
| 调试模式 | ✅ 支持 | ✅ 支持 |

## 注意事项

1. **middleware 组件**：只有 `base/middleware` 会被构建，其他 base 子组件不会单独构建
2. **Bootstrap 文件**：会自动生成，无需手动创建
3. **Promise resolver**：组件加载完成后会自动调用 resolver
4. **CDN 回退**：支持多个 CDN 主机的自动回退
5. **调试信息**：开启 debug 模式可查看详细的加载过程

## 故障排除

### 构建失败
1. 检查组件是否有有效的入口文件
2. 确认 `amis` 依赖是否正确安装
3. 查看构建日志中的具体错误信息

### Promise 不工作
1. 确认 CDN URL 是否正确
2. 检查 resolver 函数是否被正确设置
3. 验证 window 环境是否支持代理

### 组件未注册
1. 确认 `registerRenderer` 调用是否正确
2. 检查组件类型是否匹配
3. 验证 bootstrap 文件是否被正确生成
