<script setup lang="ts">
import AirstarEntry from '@airstar/entry';
import Radar from '@ks-radar/radar';
import * as Vue from 'vue'
import * as Amis from 'amis'
import * as AmisCore from 'amis-core'
import * as React from 'react'
import * as ReactDOM from 'react-dom'

// import '@polaris/components/polaris-complex-filter-v1/entry';
// import '@polaris/components/polaris-service-insight-content-realtime/entry';
// import '@polaris/components/polaris-ks-table/entry';
// import '@polaris/components/base/middleware/entry';

window.Vue = Vue
window.AmisCore = AmisCore
window.Amis = Amis
window.React = React
window.ReactDOM = ReactDOM
// 初始化 entry
function initAirstarEntry() {
    let airstarEnv = 'prod';
    if (window.location.host.includes('local')) {
        airstarEnv = 'dev';
    } else if (window.location.host.includes('staging')) {
        airstarEnv = 'stage';
    } else if (window.location.host.includes('prt')) {
        airstarEnv = 'pre';
    } else {
        airstarEnv = 'prod';
    }
    window.airstarEntry = new AirstarEntry({
        appName: 'songjiaqi03-trail-app',
        // env: airstarEnv,
        env: 'dev',
        route: [
            {
                page: '/realtime-page-new',
                root: '#airstar-content',
                matchFn: (path: string) => {
                    return path.includes('/content-insight/config-new');
                }
            },
        ],
        customConfig: {
            asset: {
                // 业务不要调整，手动打包就这一个
                cxdCss: 'https://w2.kskwai.com/kos/nlav12127/airstar-entry-assets/v001beta7/notks/airstar-entry-cxd-0514.min.ab19e85cd97a196c.css',
            }
        },
        log: {
            Radar,
            weblogger: window.weblogger,
        }
    });
}

// 通过 entry 渲染
function renderByEntry() {
    // @ts-ignore
    window.airstarEntry.proxyWindow.Vue = Vue
    // @ts-ignore
    window.airstarEntry.proxyWindow.AmisCore = AmisCore
    // @ts-ignore
    window.airstarEntry.proxyWindow.Amis = Amis
    // @ts-ignore
    window.airstarEntry.proxyWindow.React = React
    // @ts-ignore
    window.airstarEntry.proxyWindow.ReactDOM = ReactDOM
    window.airstarEntry.render('/content-insight/config-new');
}

initAirstarEntry();
renderByEntry();

</script>

<template>
    <!-- <div class="amis-custom box-transparent-panel polaris-light-theme">
    </div> -->
    <div class="amis-custom box-transparent-panel polaris-light-theme">
        123
        <div id="airstar-content" class="amis-container" />
    </div>
</template>
<style lang="less">
.entry-container {

    /* 重置所有可能被继承的属性 */
    [class*="ks-"] * {
        // /* 然后重新应用需要的继承属性 */
        // font-family: inherit;
        // line-height: inherit;
        // color: inherit;
        // // all: inherit;
        // /* 其他需要继承的属性 */
    }
}
</style>
<style lang="less" scoped>
.amis-custom {
    :deep(.amis-container) {
        .common-card {
            border-radius: 8px;
            background-color: #fff;
            padding: 16px;
            margin-bottom: 12px;
        }

        .half-bottom {
            border-radius: 0 0 8px 8px;
            padding-bottom: 0;
        }

        .half-top {
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .no-border-radius {
            border-radius: 0;
        }

        .margin-bottom0 {
            margin-bottom: 0;
        }

        .padding-bottom0 {
            padding-bottom: 0;
        }
    }

    :deep(.ks-cascader) {
        height: 32px;
        line-height: 32px;

        .ks-input__inner {
            height: 32px !important;
            line-height: 32px !important;
        }

        .ks-tag.is-closable.ks-cascader__tag {
            max-width: 150px;
        }
    }

    :deep(.cascader-channelId) {
        .ks-input {
            .ks-input__inner::placeholder {
                color: #252626;
            }
        }
    }

    // :deep(.ks-table--fit) {
    //     overflow: visible;
    // }
    // :deep(.ks-table__header-wrapper) {
    //     position: sticky;
    //     top: 0;
    //     z-index: 4;
    //     .is-group {
    //     }
    // }
    // :deep(
    //         .ks-table__header-wrapper:has(
    //                 + .ks-table__body-wrapper.is-scrolling-none
    //             )
    //     ) {
    //     position: sticky;
    //     top: 0;
    //     z-index: 10;
    //     .is-hidden {
    //         visibility: visible;
    //         & > .cell {
    //             visibility: visible;
    //         }
    //     }
    // }
    /* 表格头sticky end */
}
</style>
<style lang="less">
.flow-private-table-hover-tooltip,
.flow-private-table-hover-tooltip__small {
    img {
        width: 100%;
    }

    .content-small {
        font-size: 12px;
        color: #bbbdbf;
    }
}

.flow-private-table-hover-tooltip {
    width: 522px;
}

.flow-private-table-hover-tooltip__small {
    width: 380px;
}
</style>
